import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { modules } from './modulesData';
import ModuleStone from './ModuleStone';
import ModuleDetailModal from './ModuleDetailModal';
import { Module } from './types/GameData';

const Score: React.FC = () => {
  const navigate = useNavigate();
  const [selectedModule, setSelectedModule] = useState<Module | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const handleModuleClick = (module: Module): void => {
    setSelectedModule(module);
    setIsModalOpen(true);
  };

  const closeModal = (): void => {
    setIsModalOpen(false);
    setSelectedModule(null);
  };

  const handleBackToHome = (): void => {
    navigate('/home');
  };

  // Precise module positions that create the exact flow from the reference image
  const modulePositions = [
    { x: 120, y: 420 },   // Module 1 - Start (bottom left like reference)
    { x: 220, y: 350 },   // Module 2 - First ascending curve
    { x: 320, y: 280 },   // Module 3 - Continuing upward
    { x: 200, y: 200 },   // Module 4 - Sharp switchback left (like orange node in reference)
    { x: 350, y: 150 },   // Module 5 - Peak of the mountain
    { x: 480, y: 200 },   // Module 6 - Descending right
    { x: 580, y: 270 },   // Module 7 - Mid-level curve
    { x: 680, y: 340 },   // Module 8 - Lower right section
    { x: 580, y: 410 },   // Module 9 - Dip down (like reference)
    { x: 750, y: 350 },   // Module 10 - Rising back up
    { x: 850, y: 280 },   // Module 11 - Final ascent
    { x: 950, y: 200 },   // Module 12 - End destination (top right)
  ];

  // Generate smooth, natural winding path with improved curve calculations
  const generateContinuousPath = () => {
    let pathString = `M ${modulePositions[0].x} ${modulePositions[0].y}`;

    for (let i = 1; i < modulePositions.length; i++) {
      const current = modulePositions[i];
      const previous = modulePositions[i - 1];
      const next = i < modulePositions.length - 1 ? modulePositions[i + 1] : null;

      // Calculate vectors and distances
      const dx = current.x - previous.x;
      const dy = current.y - previous.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      // Improved curvature calculation for smoother paths
      const baseCurvature = Math.min(distance * 0.25, 80); // Limit max curvature

      // Calculate perpendicular vector for curve direction
      const perpX = -dy / distance;
      const perpY = dx / distance;

      // Determine curve direction based on overall path flow
      let direction = 1;
      if (next) {
        // Look ahead to create more natural flow
        const nextDx = next.x - current.x;
        const nextDy = next.y - current.y;
        const crossProduct = dx * nextDy - dy * nextDx;
        direction = crossProduct > 0 ? 1 : -1;
      } else {
        // Alternate for S-pattern when no next point
        direction = i % 2 === 0 ? 1 : -1;
      }

      // Smooth control point calculation for natural curves
      // First control point (from previous point)
      const cp1x = previous.x + dx * 0.3 + perpX * baseCurvature * direction * 0.8;
      const cp1y = previous.y + dy * 0.3 + perpY * baseCurvature * direction * 0.8;

      // Second control point (approaching current point)
      const cp2x = current.x - dx * 0.3 + perpX * baseCurvature * direction * 0.8;
      const cp2y = current.y - dy * 0.3 + perpY * baseCurvature * direction * 0.8;

      pathString += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${current.x} ${current.y}`;
    }

    return pathString;
  };

  const pathData = generateContinuousPath();

  const getPathPositions = () => modulePositions;

  const calculatedPositions = getPathPositions();



  return (
    <div className="w-full h-screen bg-gradient-to-br from-yellow-200 via-orange-200 to-amber-300 overflow-hidden">
      <div className="relative w-full h-full">
        <svg
          width="100%"
          height="100%"
          viewBox="0 0 1200 600"
          className="absolute inset-0"
          preserveAspectRatio="xMidYMid meet"
        >
          {/* Desert background pattern */}
          <defs>
            <pattern id="sandPattern" patternUnits="userSpaceOnUse" width="40" height="40">
              <circle cx="5" cy="5" r="1" fill="#F59E0B" opacity="0.1" />
              <circle cx="15" cy="15" r="0.8" fill="#EA580C" opacity="0.1" />
              <circle cx="25" cy="8" r="1.2" fill="#F97316" opacity="0.1" />
              <circle cx="35" cy="25" r="0.9" fill="#FB923C" opacity="0.1" />
            </pattern>
            
            <filter id="pathShadow">
              <feDropShadow dx="2" dy="4" stdDeviation="3" floodColor="#92400E" floodOpacity="0.3"/>
            </filter>

            <linearGradient id="pathGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#D4A574" />
              <stop offset="50%" stopColor="#E8C4A0" />
              <stop offset="100%" stopColor="#D4A574" />
            </linearGradient>
          </defs>
          
          {/* Background with sand pattern */}
          <rect width="100%" height="100%" fill="url(#sandPattern)" />

          {/* Landscape elements positioned away from path */}
          <ellipse cx="150" cy="550" rx="80" ry="20" fill="#D2691E" opacity="0.2" />
          <ellipse cx="450" cy="580" rx="100" ry="25" fill="#CD853F" opacity="0.18" />
          <ellipse cx="750" cy="560" rx="90" ry="22" fill="#DEB887" opacity="0.2" />
          <ellipse cx="1050" cy="570" rx="95" ry="24" fill="#D2691E" opacity="0.19" />

          {/* Small decorative elements scattered around (not on path) */}
          <circle cx="80" cy="500" r="4" fill="#8B4513" opacity="0.3" />
          <circle cx="380" cy="480" r="3" fill="#A0522D" opacity="0.3" />
          <circle cx="680" cy="520" r="4" fill="#8B4513" opacity="0.3" />
          <circle cx="280" cy="350" r="3" fill="#A0522D" opacity="0.3" />
          <circle cx="880" cy="300" r="4" fill="#8B4513" opacity="0.3" />

          {/* Additional small rocks for desert atmosphere */}
          <circle cx="50" cy="400" r="2" fill="#A0522D" opacity="0.25" />
          <circle cx="1000" cy="450" r="3" fill="#8B4513" opacity="0.25" />
          <circle cx="500" cy="120" r="2" fill="#A0522D" opacity="0.25" />
          
          {/* Path shadow (bottom layer) */}
          <path
            d={pathData}
            fill="none"
            stroke="#8B4513"
            strokeWidth="55"
            strokeLinecap="round"
            strokeLinejoin="round"
            opacity="0.4"
            transform="translate(3, 6)"
          />

          {/* Path outer border (dark brown) */}
          <path
            d={pathData}
            fill="none"
            stroke="#A0522D"
            strokeWidth="50"
            strokeLinecap="round"
            strokeLinejoin="round"
            opacity="0.8"
          />

          {/* Path main body (tan/beige like reference) */}
          <path
            d={pathData}
            fill="none"
            stroke="#DEB887"
            strokeWidth="40"
            strokeLinecap="round"
            strokeLinejoin="round"
            opacity="1"
          />

          {/* Path inner highlight (lighter tan) */}
          <path
            d={pathData}
            fill="none"
            stroke="#F5DEB3"
            strokeWidth="30"
            strokeLinecap="round"
            strokeLinejoin="round"
            opacity="0.9"
          />

          {/* White center highlight like reference */}
          <path
            d={pathData}
            fill="none"
            stroke="#FFFFFF"
            strokeWidth="20"
            strokeLinecap="round"
            strokeLinejoin="round"
            opacity="0.6"
          />

          {/* Dashed center line exactly like reference */}
          <path
            d={pathData}
            fill="none"
            stroke="#FFFFFF"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeDasharray="12,8"
            opacity="1"
          />
          
          
          {/* Module stones */}
          {modules.map((module: Module) => (
            <ModuleStone
              key={module.id}
              module={module}
              position={calculatedPositions[module.id - 1]}
              onClick={() => handleModuleClick(module)}
            />
          ))}
        </svg>

        {/* Back Button */}
        <div className="absolute top-6 left-6 z-20">
          <button
            onClick={handleBackToHome}
            className="bg-white bg-opacity-90 hover:bg-opacity-100 text-amber-900 font-bold py-2 px-4 rounded-lg shadow-lg transition-all duration-200 hover:scale-105 flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </button>
        </div>

        
        {/* Progress indicator */}
        <div className="absolute top-8 right-8 z-10 bg-white bg-opacity-90 rounded-lg p-4 shadow-lg">
          <div className="text-sm font-medium text-gray-700 mb-2">Overall Progress</div>
          <div className="flex space-x-1">
            {modules.map((module) => (
              <div
                key={module.id}
                className={`w-3 h-3 rounded-full ${
                  module.status === 'completed' ? 'bg-green-500' :
                  module.status === 'unlocked' ? 'bg-yellow-500' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
          <div className="text-xs text-gray-600 mt-1">
            {modules.filter(m => m.status === 'completed').length} / {modules.length} completed
          </div>
        </div>
      </div>

      {/* Modal */}
      <ModuleDetailModal
        isOpen={isModalOpen}
        module={selectedModule}
        onClose={closeModal}
      />
    </div>
  );
};

export default Score;